// Database Service
// Handles all database operations for users and connections

const User = require('../models/User');
const Connection = require('../models/Connection');

class DatabaseService {
  constructor() {
    console.log('🔧 Database Service initialized');
  }

  // ===== USER OPERATIONS =====

  // Create or update user
  async createOrUpdateUser(userData) {
    try {
      console.log('💾 Creating/updating user:', userData.slackUserId);

      const existingUser = await User.findBySlackId(userData.slackUserId);
      
      if (existingUser) {
        // Update existing user
        Object.assign(existingUser, userData);
        existingUser.activity.lastActiveAt = new Date();
        await existingUser.save();
        
        console.log('✅ User updated successfully');
        return existingUser;
      } else {
        // Create new user
        const newUser = new User({
          ...userData,
          externalUserId: userData.externalUserId || userData.slackUserId,
          activity: {
            lastActiveAt: new Date(),
            totalQueries: 0,
            lastQueryAt: null,
            favoriteApps: []
          }
        });
        
        await newUser.save();
        console.log('✅ New user created successfully');
        return newUser;
      }
    } catch (error) {
      console.error('❌ Error creating/updating user:', error.message);
      throw error;
    }
  }

  // Get user by Slack ID
  async getUserBySlackId(slackUserId) {
    try {
      const user = await User.findBySlackId(slackUserId);
      return user;
    } catch (error) {
      console.error('❌ Error getting user by Slack ID:', error.message);
      throw error;
    }
  }

  // Get user by external ID
  async getUserByExternalId(externalUserId) {
    try {
      const user = await User.findByExternalId(externalUserId);
      return user;
    } catch (error) {
      console.error('❌ Error getting user by external ID:', error.message);
      throw error;
    }
  }

  // Update user authentication status
  async updateUserAuth(slackUserId, authType, authData) {
    try {
      console.log(`🔐 Updating ${authType} auth for user:`, slackUserId);

      const user = await User.findBySlackId(slackUserId);
      if (!user) {
        throw new Error('User not found');
      }

      user.authStatus[authType] = {
        ...user.authStatus[authType],
        ...authData,
        connected: true,
        connectedAt: new Date()
      };

      // Update primary email if provided
      if (authType === 'slack' && authData.email) {
        user.slackEmail = authData.email;
      } else if (authType === 'pipedream' && authData.email) {
        user.pipedreamEmail = authData.email;
      }

      await user.save();
      console.log('✅ User auth updated successfully');
      return user;
    } catch (error) {
      console.error('❌ Error updating user auth:', error.message);
      throw error;
    }
  }

  // ===== CONNECTION OPERATIONS =====

  // Store user connection
  async storeConnection(connectionData) {
    try {
      console.log('💾 Storing connection:', {
        user: connectionData.slackUserId,
        app: connectionData.appName,
        accountId: connectionData.accountId
      });

      // Check if connection already exists
      const existingConnection = await Connection.findByUserAndApp(
        connectionData.slackUserId, 
        connectionData.appName
      );

      if (existingConnection) {
        // Update existing connection
        Object.assign(existingConnection, connectionData);
        existingConnection.status = 'active';
        existingConnection.isHealthy = true;
        existingConnection.lastSyncAt = new Date();
        await existingConnection.save();

        console.log('✅ Connection updated successfully');
        return existingConnection;
      } else {
        // Create new connection
        const newConnection = new Connection(connectionData);
        await newConnection.save();

        // Update user connection stats
        const user = await User.findBySlackId(connectionData.slackUserId);
        if (user) {
          await user.addConnection(connectionData.appName);
        }

        console.log('✅ New connection created successfully');
        return newConnection;
      }
    } catch (error) {
      console.error('❌ Error storing connection:', error.message);
      throw error;
    }
  }

  // Get user connections
  async getUserConnections(slackUserId) {
    try {
      console.log('🔍 Getting connections for user:', slackUserId);

      const connections = await Connection.findByUser(slackUserId);
      
      console.log(`✅ Found ${connections.length} connections`);
      return connections;
    } catch (error) {
      console.error('❌ Error getting user connections:', error.message);
      throw error;
    }
  }

  // Get user connection by app
  async getUserConnectionByApp(slackUserId, appName) {
    try {
      const connection = await Connection.findByUserAndApp(slackUserId, appName);
      return connection;
    } catch (error) {
      console.error('❌ Error getting user connection by app:', error.message);
      throw error;
    }
  }

  // Get dynamic credentials for API calls
  async getDynamicCredentials(slackUserId, fallbackEmail = null) {
    try {
      console.log('🔍 Getting dynamic credentials for user:', slackUserId);

      const user = await User.findBySlackId(slackUserId);
      const connections = await Connection.findByUser(slackUserId);

      // Extract account IDs from connections
      const accountIds = connections.map(conn => conn.accountId);
      
      // Use user's primary email or fallback
      const userEmail = user?.primaryEmail || fallbackEmail || '<EMAIL>';
      
      // Use user's external ID
      const externalUserId = user?.externalUserId || slackUserId;

      const credentials = {
        account_ids: accountIds.length > 0 ? accountIds : ['default_account'],
        external_user_id: externalUserId,
        user_email: userEmail,
        connected_apps: connections.map(conn => conn.appName),
        total_connections: connections.length,
        source: accountIds.length > 0 ? 'dynamic_connections' : 'fallback_default'
      };

      console.log('✅ Dynamic credentials generated:', {
        accountIds: credentials.account_ids.length,
        connectedApps: credentials.connected_apps.length,
        source: credentials.source
      });

      return credentials;
    } catch (error) {
      console.error('❌ Error getting dynamic credentials:', error.message);
      throw error;
    }
  }

  // Update connection usage
  async updateConnectionUsage(slackUserId, appName, success = true, responseTime = 0) {
    try {
      const connection = await Connection.findByUserAndApp(slackUserId, appName);
      if (connection) {
        await connection.updateUsage(success, responseTime);
        
        // Update user activity
        const user = await User.findBySlackId(slackUserId);
        if (user) {
          await user.updateActivity();
        }
      }
    } catch (error) {
      console.error('❌ Error updating connection usage:', error.message);
    }
  }

  // Disconnect user connection
  async disconnectUserConnection(slackUserId, appName) {
    try {
      console.log('🔌 Disconnecting user connection:', { slackUserId, appName });

      const connection = await Connection.findByUserAndApp(slackUserId, appName);
      if (connection) {
        await connection.disconnect();

        // Update user connection stats
        const user = await User.findBySlackId(slackUserId);
        if (user) {
          await user.removeConnection(appName);
        }

        console.log('✅ Connection disconnected successfully');
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Error disconnecting user connection:', error.message);
      throw error;
    }
  }

  // ===== STATUS AND STATISTICS =====

  // Get user status
  async getUserStatus(slackUserId) {
    try {
      const user = await User.findBySlackId(slackUserId);
      const connections = await Connection.findByUser(slackUserId);

      if (!user) {
        return {
          found: false,
          message: 'User not found'
        };
      }

      const status = {
        found: true,
        user: user.getConnectionSummary(),
        connections: connections.map(conn => conn.getConnectionSummary()),
        summary: {
          totalConnections: connections.length,
          activeConnections: connections.filter(c => c.status === 'active').length,
          connectedApps: connections.map(c => c.appName),
          slackConnected: user.authStatus.slack.connected,
          pipedreamConnected: user.authStatus.pipedream.connected,
          lastActive: user.activity.lastActiveAt
        }
      };

      return status;
    } catch (error) {
      console.error('❌ Error getting user status:', error.message);
      throw error;
    }
  }

  // Get global statistics
  async getGlobalStats() {
    try {
      const userStats = await User.getConnectionStats();
      const connectionStats = await Connection.getGlobalStats();

      return {
        users: userStats[0] || {},
        connections: connectionStats[0] || {},
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Error getting global stats:', error.message);
      throw error;
    }
  }

  // Health check
  async healthCheck() {
    try {
      // Test database connectivity
      const userCount = await User.countDocuments();
      const connectionCount = await Connection.countDocuments();

      return {
        status: 'healthy',
        database: 'connected',
        collections: {
          users: userCount,
          connections: connectionCount
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Database health check failed:', error.message);
      return {
        status: 'unhealthy',
        database: 'disconnected',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
const databaseService = new DatabaseService();

module.exports = databaseService;
