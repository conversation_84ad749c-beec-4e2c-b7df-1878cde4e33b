// Connection Model
// Stores user connections to various apps and their account IDs

const mongoose = require('mongoose');

const connectionSchema = new mongoose.Schema({
  // User Reference
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
    description: 'Reference to User document'
  },
  
  slackUserId: {
    type: String,
    required: true,
    index: true,
    description: 'Slack user ID for quick lookup'
  },
  
  externalUserId: {
    type: String,
    required: true,
    index: true,
    description: 'External user ID for API calls'
  },

  // App Information
  appName: {
    type: String,
    required: true,
    index: true,
    description: 'Name of the connected app (e.g., google_drive, slack, dropbox)'
  },
  
  appDisplayName: {
    type: String,
    required: false,
    description: 'Human-readable app name'
  },
  
  appCategory: {
    type: String,
    required: false,
    enum: ['storage', 'communication', 'productivity', 'project_management', 'support', 'other'],
    description: 'App category for organization'
  },

  // Account Information
  accountId: {
    type: String,
    required: true,
    index: true,
    description: 'Real account ID from Pipedream (e.g., apn_KAhZVzn)'
  },
  
  accountName: {
    type: String,
    required: false,
    description: 'Account name or email from the connected service'
  },
  
  accountEmail: {
    type: String,
    required: false,
    index: true,
    description: 'Email associated with the connected account'
  },

  // Connection Details
  connectionSource: {
    type: String,
    required: true,
    enum: ['pipedream_oauth', 'pipedream_connect', 'slack_oauth', 'manual', 'webhook'],
    default: 'pipedream_connect',
    description: 'How the connection was established'
  },
  
  connectionData: {
    type: mongoose.Schema.Types.Mixed,
    default: {},
    description: 'Additional connection metadata'
  },

  // Authentication Information
  authInfo: {
    accessToken: { type: String, default: null },
    refreshToken: { type: String, default: null },
    tokenType: { type: String, default: 'Bearer' },
    expiresAt: { type: Date, default: null },
    scopes: [{ type: String }]
  },

  // Connection Status
  status: {
    type: String,
    required: true,
    enum: ['active', 'inactive', 'expired', 'revoked', 'error'],
    default: 'active',
    index: true,
    description: 'Current connection status'
  },
  
  isHealthy: {
    type: Boolean,
    default: true,
    index: true,
    description: 'Whether the connection is working properly'
  },
  
  lastHealthCheck: {
    type: Date,
    default: Date.now,
    description: 'Last time connection health was verified'
  },

  // Usage Statistics
  usage: {
    totalApiCalls: { type: Number, default: 0 },
    lastUsedAt: { type: Date, default: null },
    successfulCalls: { type: Number, default: 0 },
    failedCalls: { type: Number, default: 0 },
    averageResponseTime: { type: Number, default: 0 }
  },

  // Timestamps
  connectedAt: { type: Date, default: Date.now },
  lastSyncAt: { type: Date, default: null },
  disconnectedAt: { type: Date, default: null },
  
  // Metadata
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  
  // Additional data storage
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  collection: 'connections'
});

// Compound Indexes for better performance
connectionSchema.index({ slackUserId: 1, appName: 1 }, { unique: true });
connectionSchema.index({ externalUserId: 1, appName: 1 });
connectionSchema.index({ status: 1, isHealthy: 1 });
connectionSchema.index({ connectedAt: 1 });
connectionSchema.index({ 'usage.lastUsedAt': 1 });

// Virtual for connection age
connectionSchema.virtual('connectionAge').get(function() {
  return Date.now() - this.connectedAt.getTime();
});

// Virtual for connection health status
connectionSchema.virtual('healthStatus').get(function() {
  if (!this.isHealthy) return 'unhealthy';
  if (this.status !== 'active') return 'inactive';
  if (this.authInfo.expiresAt && this.authInfo.expiresAt < new Date()) return 'expired';
  return 'healthy';
});

// Instance Methods
connectionSchema.methods.updateUsage = function(success = true, responseTime = 0) {
  this.usage.totalApiCalls += 1;
  this.usage.lastUsedAt = new Date();
  
  if (success) {
    this.usage.successfulCalls += 1;
  } else {
    this.usage.failedCalls += 1;
  }
  
  // Update average response time
  if (responseTime > 0) {
    const totalCalls = this.usage.totalApiCalls;
    this.usage.averageResponseTime = 
      ((this.usage.averageResponseTime * (totalCalls - 1)) + responseTime) / totalCalls;
  }
  
  return this.save();
};

connectionSchema.methods.markAsHealthy = function() {
  this.isHealthy = true;
  this.lastHealthCheck = new Date();
  if (this.status === 'error') {
    this.status = 'active';
  }
  return this.save();
};

connectionSchema.methods.markAsUnhealthy = function(reason = null) {
  this.isHealthy = false;
  this.lastHealthCheck = new Date();
  this.status = 'error';
  if (reason) {
    this.metadata.lastError = {
      message: reason,
      timestamp: new Date()
    };
  }
  return this.save();
};

connectionSchema.methods.disconnect = function() {
  this.status = 'inactive';
  this.disconnectedAt = new Date();
  this.isHealthy = false;
  return this.save();
};

connectionSchema.methods.getConnectionSummary = function() {
  return {
    id: this._id,
    appName: this.appName,
    appDisplayName: this.appDisplayName,
    accountId: this.accountId,
    accountName: this.accountName,
    accountEmail: this.accountEmail,
    status: this.status,
    isHealthy: this.isHealthy,
    connectedAt: this.connectedAt,
    lastUsedAt: this.usage.lastUsedAt,
    totalApiCalls: this.usage.totalApiCalls,
    connectionSource: this.connectionSource
  };
};

// Static Methods
connectionSchema.statics.findByUser = function(slackUserId) {
  return this.find({ slackUserId, status: 'active' });
};

connectionSchema.statics.findByUserAndApp = function(slackUserId, appName) {
  return this.findOne({ slackUserId, appName, status: 'active' });
};

connectionSchema.statics.findByAccountId = function(accountId) {
  return this.find({ accountId });
};

connectionSchema.statics.getActiveConnections = function() {
  return this.find({ status: 'active', isHealthy: true });
};

connectionSchema.statics.getConnectionsByApp = function(appName) {
  return this.find({ appName, status: 'active' });
};

connectionSchema.statics.getUserConnectionStats = function(slackUserId) {
  return this.aggregate([
    { $match: { slackUserId } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        apps: { $addToSet: '$appName' }
      }
    }
  ]);
};

connectionSchema.statics.getGlobalStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: null,
        totalConnections: { $sum: 1 },
        activeConnections: { 
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        uniqueUsers: { $addToSet: '$slackUserId' },
        uniqueApps: { $addToSet: '$appName' },
        totalApiCalls: { $sum: '$usage.totalApiCalls' },
        averageConnectionAge: { 
          $avg: { $subtract: [new Date(), '$connectedAt'] }
        }
      }
    },
    {
      $project: {
        totalConnections: 1,
        activeConnections: 1,
        uniqueUsers: { $size: '$uniqueUsers' },
        uniqueApps: { $size: '$uniqueApps' },
        totalApiCalls: 1,
        averageConnectionAge: 1
      }
    }
  ]);
};

// Pre-save middleware
connectionSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Set app display name if not provided
  if (!this.appDisplayName) {
    this.appDisplayName = this.appName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }
  
  // Set app category based on app name
  if (!this.appCategory) {
    const categoryMap = {
      'google_drive': 'storage',
      'dropbox': 'storage',
      'slack': 'communication',
      'microsoft_teams': 'communication',
      'jira': 'project_management',
      'confluence': 'productivity',
      'zendesk': 'support',
      'document360': 'support',
      'sharepoint': 'storage',
      'gmail': 'communication'
    };
    this.appCategory = categoryMap[this.appName] || 'other';
  }
  
  next();
});

// Export the model
const Connection = mongoose.model('Connection', connectionSchema);

module.exports = Connection;
