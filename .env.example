# ===== SL<PERSON>K BOT CONFIGURATION =====
# Get these from your Slack App settings: https://api.slack.com/apps
SLACK_BOT_TOKEN=xoxb-your-bot-token-here
SLACK_SIGNING_SECRET=your-signing-secret-here
SLACK_APP_TOKEN=xapp-your-app-token-here

# ===== SERVER CONFIGURATION =====
PORT=3000
NODE_ENV=development

# ===== DATABASE CONFIGURATION =====
# MongoDB connection string
MONGODB_URI=mongodb://localhost:27017/slack-enterprise-search
# Alternative for MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/slack-enterprise-search

# ===== ENTERPRISE SEARCH API CONFIGURATION =====
# Your Enterprise Search API endpoint and credentials
API_BASE_URL=https://qa-es-api.kroolo.com
API_KEY=your-api-key-here

# ===== AI-POWERED NATURAL LANGUAGE PROCESSING =====
# OpenAI API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# Google Gemini API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your-gemini-api-key-here

# ===== AI CONFIGURATION =====
# Choose which AI provider to use for natural language processing
AI_PROVIDER=gemini  # Options: openai, gemini, both
AI_MODEL_OPENAI=gpt-3.5-turbo
AI_MODEL_GEMINI=gemini-pro

# ===== PIPEDREAM DYNAMIC AUTHENTICATION =====
# Get these from your Pipedream OAuth app: https://pipedream.com/apps
PIPEDREAM_CLIENT_ID=your-pipedream-client-id-here
PIPEDREAM_PROJECT_ID=your-pipedream-project-id-here
PIPEDREAM_CLIENT_SECRET=your-pipedream-client-secret-here
PIPEDREAM_ENV=development
PIPEDREAM_API_BASE=https://api.pipedream.com/v1

# ===== SLACK OAUTH AUTHENTICATION =====
# Get these from your Slack App OAuth settings: https://api.slack.com/apps
# Used for connecting individual user Slack apps (different from bot token above)
SLACK_CLIENT_ID=your-slack-oauth-client-id-here
SLACK_CLIENT_SECRET=your-slack-oauth-client-secret-here

# ===== SLACK BOT URL =====
# URL where your bot server is running (for OAuth callbacks)
# For local development: http://localhost:3000
# For production: https://your-domain.com
SLACK_BOT_URL=http://localhost:3000

# ===== NOTES =====
# Enterprise Search API is configured with Triple RBAC in src/config/apis.js:
# - account_ids: Array of account IDs user has access to
# - external_user_id: User's external ID
# - user_email: User's email address
#
# The bot supports both static and dynamic user credentials:
# - Static: Uses predefined credentials for all users
# - Dynamic: Uses individual user credentials via Pipedream OAuth
#
# Setup Instructions:
# 1. Copy this file to .env
# 2. Fill in your actual API keys and credentials
# 3. Configure your Pipedream OAuth app with redirect URI: http://localhost:3000/auth/pipedream/callback
# 4. Configure your Slack OAuth app with redirect URI: http://localhost:3000/auth/slack/callback
# 5. Start the server: npm run server
# 6. Start the bot: npm start
# 7. Test Pipedream: @SmartBot connect pipedream
# 8. Test Slack: @SmartBot connect slack
